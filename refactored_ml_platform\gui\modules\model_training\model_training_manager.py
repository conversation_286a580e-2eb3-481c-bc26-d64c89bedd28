#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型训练管理器
整合模型训练的各个功能模块
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, Optional, List
import threading
import time
import pandas as pd
import numpy as np

from ...core.base_gui import BaseGUI
from ...core.component_factory import get_component_factory
from ...core.event_manager import get_event_manager, EventTypes
from ...components.progress_widgets import ProgressWidget

# 导入算法模块
try:
    from algorithms import MODEL_TRAINERS, MODEL_NAMES, MODEL_DISPLAY_NAMES
    from algorithms.data_preprocessing import preprocess_data
    from algorithms.hyperparameter_tuning import tune_model
    HAS_ALGORITHMS = True
except ImportError:
    # 备用导入方式
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent.parent.parent
    sys.path.insert(0, str(project_root))

    try:
        from algorithms import MODEL_TRAINERS, MODEL_NAMES, MODEL_DISPLAY_NAMES
        from algorithms.data_preprocessing import preprocess_data
        from algorithms.hyperparameter_tuning import tune_model
        HAS_ALGORITHMS = True
    except ImportError:
        MODEL_TRAINERS = {}
        MODEL_NAMES = []
        MODEL_DISPLAY_NAMES = {}
        HAS_ALGORITHMS = False

# 导入线程安全GUI工具
try:
    from utils.thread_safe_gui import ThreadSafeGUI
except ImportError:
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent.parent.parent
    sys.path.insert(0, str(project_root))
    from utils.thread_safe_gui import ThreadSafeGUI


class ModelTrainingManager(BaseGUI):
    """模型训练管理器类"""

    def __init__(self, parent):
        """初始化模型训练管理器"""
        self.current_data = None
        self.preprocessed_data = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.feature_names = []
        self.target_column = None

        self.training_results = {}
        self.selected_models = []
        self.training_config = {
            'test_size': 0.2,
            'random_state': 42,
            'scaling_method': 'standard',
            'cross_validation': True,
            'cv_folds': 5,
            'enable_hyperparameter_tuning': False,
            'n_trials': 50
        }
        self.is_training = False
        self.training_thread = None
        self.gui_updater = None

        super().__init__(parent)

        # 初始化GUI更新器
        if parent:
            self.gui_updater = ThreadSafeGUI(parent)

        # 订阅相关事件
        self._bind_events()
    
    def _setup_ui(self):
        """设置UI界面"""
        factory = get_component_factory()

        # 主框架
        if self.parent:
            self.main_frame = factory.create_frame(self.parent)
            self.main_frame.pack(fill='both', expand=True, padx=5, pady=5)
        else:
            self.main_frame = None
            return

        # 创建主要内容区域
        self._create_main_content()
    
    def _create_main_content(self):
        """创建主要内容区域"""
        factory = get_component_factory()

        # 创建水平分割面板
        paned_window = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill='both', expand=True)

        # 左侧配置面板
        self._create_config_panel(paned_window)

        # 右侧结果面板
        self._create_results_panel(paned_window)

        self.register_component('paned_window', paned_window)
    
    def _create_config_panel(self, parent):
        """创建左侧配置面板"""
        factory = get_component_factory()

        # 配置面板框架
        config_frame = factory.create_frame(parent)
        config_frame.pack(fill='both', expand=True)

        # 创建滚动区域
        canvas = tk.Canvas(config_frame, width=350)
        scrollbar = ttk.Scrollbar(config_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = factory.create_frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 状态信息
        self._create_status_section(scrollable_frame)

        # 模型选择
        self._create_model_selection_section(scrollable_frame)

        # 训练配置
        self._create_training_config_section(scrollable_frame)

        # 控制按钮
        self._create_control_buttons_section(scrollable_frame)

        # 将配置面板添加到分割窗口
        parent.add(config_frame, weight=1)

        self.register_component('config_frame', config_frame)
    
    def _create_status_section(self, parent):
        """创建状态信息区域"""
        factory = get_component_factory()

        status_frame = factory.create_labelframe(parent, text="📊 训练状态")
        status_frame.pack(fill='x', padx=10, pady=5)

        self.status_label = factory.create_label(status_frame, text="等待数据加载...", style='info')
        self.status_label.pack(padx=10, pady=10)

        # 进度条
        self.progress_widget = ProgressWidget(status_frame, show_percentage=True)
        if self.progress_widget.main_frame:
            self.progress_widget.main_frame.pack(fill='x', padx=10, pady=(0, 10))

    def _create_model_selection_section(self, parent):
        """创建模型选择区域"""
        factory = get_component_factory()

        model_frame = factory.create_labelframe(parent, text="🤖 模型选择")
        model_frame.pack(fill='x', padx=10, pady=5)

        # 全选/取消全选按钮
        button_frame = factory.create_frame(model_frame)
        button_frame.pack(fill='x', padx=10, pady=5)

        select_all_btn = factory.create_button(button_frame, text="全选",
                                             command=self._select_all_models, style='small')
        select_all_btn.pack(side='left', padx=(0, 5))

        deselect_all_btn = factory.create_button(button_frame, text="取消全选",
                                                command=self._deselect_all_models, style='small')
        deselect_all_btn.pack(side='left')

        # 模型复选框
        self.model_vars = {}
        models_info = {
            'DecisionTree': '决策树 - 快速、可解释',
            'RandomForest': '随机森林 - 高精度、抗过拟合',
            'XGBoost': 'XGBoost - 梯度提升、高性能',
            'LightGBM': 'LightGBM - 快速梯度提升',
            'CatBoost': 'CatBoost - 处理类别特征',
            'LogisticRegression': '逻辑回归 - 简单、快速',
            'SVM': '支持向量机 - 适合小数据集',
            'KNN': 'K近邻 - 非参数方法',
            'NaiveBayes': '朴素贝叶斯 - 适合文本分类',
            'NeuralNet': '神经网络 - 复杂模式识别'
        }

        for model_name, description in models_info.items():
            var = tk.BooleanVar()
            self.model_vars[model_name] = var

            cb = factory.create_checkbutton(model_frame, text=description, variable=var)
            cb.pack(anchor='w', padx=20, pady=2)

    def _create_training_config_section(self, parent):
        """创建训练配置区域"""
        factory = get_component_factory()

        config_frame = factory.create_labelframe(parent, text="⚙️ 训练配置")
        config_frame.pack(fill='x', padx=10, pady=5)

        # 测试集比例
        test_size_frame = factory.create_frame(config_frame)
        test_size_frame.pack(fill='x', padx=10, pady=5)

        factory.create_label(test_size_frame, text="测试集比例:").pack(side='left')
        self.test_size_var = tk.DoubleVar(value=0.2)
        test_size_scale = tk.Scale(test_size_frame, from_=0.1, to=0.5, resolution=0.05,
                                 orient='horizontal', variable=self.test_size_var)
        test_size_scale.pack(side='right', fill='x', expand=True, padx=(10, 0))

        # 随机种子
        seed_frame = factory.create_frame(config_frame)
        seed_frame.pack(fill='x', padx=10, pady=5)

        factory.create_label(seed_frame, text="随机种子:").pack(side='left')
        self.random_state_var = tk.IntVar(value=42)
        seed_entry = factory.create_entry(seed_frame, textvariable=self.random_state_var, width=10)
        seed_entry.pack(side='right')

        # 缩放方法
        scaling_frame = factory.create_frame(config_frame)
        scaling_frame.pack(fill='x', padx=10, pady=5)

        factory.create_label(scaling_frame, text="特征缩放:").pack(side='left')
        self.scaling_var = tk.StringVar(value='standard')
        scaling_combo = ttk.Combobox(scaling_frame, textvariable=self.scaling_var,
                                   values=['standard', 'minmax', 'robust', 'none'],
                                   state='readonly', width=12)
        scaling_combo.pack(side='right')

        # 交叉验证
        cv_frame = factory.create_frame(config_frame)
        cv_frame.pack(fill='x', padx=10, pady=5)

        self.cv_var = tk.BooleanVar(value=True)
        cv_cb = factory.create_checkbutton(cv_frame, text="启用交叉验证", variable=self.cv_var)
        cv_cb.pack(side='left')

        self.cv_folds_var = tk.IntVar(value=5)
        factory.create_label(cv_frame, text="折数:").pack(side='right', padx=(10, 5))
        cv_folds_spin = tk.Spinbox(cv_frame, from_=3, to=10, width=5, textvariable=self.cv_folds_var)
        cv_folds_spin.pack(side='right')

    def _create_control_buttons_section(self, parent):
        """创建控制按钮区域"""
        factory = get_component_factory()

        button_frame = factory.create_frame(parent)
        button_frame.pack(fill='x', padx=10, pady=10)

        # 开始训练按钮
        self.train_button = factory.create_button(
            button_frame,
            text="🚀 开始训练",
            command=self._start_training,
            style='primary'
        )
        self.train_button.pack(fill='x', pady=(0, 5))
        self.train_button.config(state='disabled')

        # 停止训练按钮
        self.stop_button = factory.create_button(
            button_frame,
            text="⏹️ 停止训练",
            command=self._stop_training,
            style='danger'
        )
        self.stop_button.pack(fill='x', pady=(0, 5))
        self.stop_button.config(state='disabled')

        # 清空结果按钮
        clear_button = factory.create_button(
            button_frame,
            text="🗑️ 清空结果",
            command=self.clear_results,
            style='secondary'
        )
        clear_button.pack(fill='x')

    def _create_results_panel(self, parent):
        """创建右侧结果面板"""
        factory = get_component_factory()

        # 结果面板框架
        results_frame = factory.create_frame(parent)
        results_frame.pack(fill='both', expand=True)

        # 创建标签页
        self.results_notebook = factory.create_notebook(results_frame)
        self.results_notebook.pack(fill='both', expand=True, padx=5, pady=5)

        # 训练日志标签页
        self._create_training_log_tab()

        # 训练结果标签页
        self._create_training_results_tab()

        # 模型比较标签页
        self._create_model_comparison_tab()

        # 将结果面板添加到分割窗口
        parent.add(results_frame, weight=2)

        self.register_component('results_frame', results_frame)

    def _create_training_log_tab(self):
        """创建训练日志标签页"""
        factory = get_component_factory()

        log_frame = factory.create_frame(self.results_notebook)
        self.results_notebook.add(log_frame, text="📋 训练日志")

        # 日志文本区域
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, font=('Consolas', 9))
        log_scrollbar = ttk.Scrollbar(log_frame, orient='vertical', command=self.log_text.yview)
        self.log_text.config(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side='left', fill='both', expand=True, padx=(5, 0), pady=5)
        log_scrollbar.pack(side='right', fill='y', padx=(0, 5), pady=5)

        self.log_text.insert('1.0', "训练日志将在这里显示...\n")
        self.log_text.config(state='disabled')

    def _create_training_results_tab(self):
        """创建训练结果标签页"""
        factory = get_component_factory()

        results_frame = factory.create_frame(self.results_notebook)
        self.results_notebook.add(results_frame, text="📊 训练结果")

        # 结果表格
        columns = ('模型', '准确率', 'F1分数', 'AUC', '训练时间')
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=15)

        # 设置列标题
        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=100, anchor='center')

        # 滚动条
        results_scrollbar = ttk.Scrollbar(results_frame, orient='vertical', command=self.results_tree.yview)
        self.results_tree.config(yscrollcommand=results_scrollbar.set)

        self.results_tree.pack(side='left', fill='both', expand=True, padx=(5, 0), pady=5)
        results_scrollbar.pack(side='right', fill='y', padx=(0, 5), pady=5)

    def _create_model_comparison_tab(self):
        """创建模型比较标签页"""
        factory = get_component_factory()

        comparison_frame = factory.create_frame(self.results_notebook)
        self.results_notebook.add(comparison_frame, text="🔍 模型比较")

        # 比较图表区域（占位符）
        comparison_label = factory.create_label(
            comparison_frame,
            text="模型比较图表将在训练完成后显示\n\n包括：\n• 性能指标对比\n• ROC曲线比较\n• 混淆矩阵对比\n• 特征重要性分析",
            style='secondary'
        )
        comparison_label.pack(expand=True)

    def _bind_events(self):
        """绑定事件"""
        event_manager = get_event_manager()
        event_manager.subscribe(EventTypes.DATA_LOADED, self._on_data_loaded)
        event_manager.subscribe(EventTypes.DATA_PREPROCESSED, self._on_data_preprocessed)
    
    def _select_all_models(self):
        """全选所有模型"""
        for var in self.model_vars.values():
            var.set(True)

    def _deselect_all_models(self):
        """取消选择所有模型"""
        for var in self.model_vars.values():
            var.set(False)

    def _start_training(self):
        """开始训练"""
        if not self.current_data:
            self.show_warning("警告", "请先加载数据！")
            return

        # 获取选中的模型
        selected_models = [name for name, var in self.model_vars.items() if var.get()]
        if not selected_models:
            self.show_warning("警告", "请至少选择一个模型！")
            return

        # 更新训练配置
        self.training_config.update({
            'test_size': self.test_size_var.get(),
            'random_state': self.random_state_var.get(),
            'scaling_method': self.scaling_var.get(),
            'cross_validation': self.cv_var.get(),
            'cv_folds': self.cv_folds_var.get()
        })

        # 更新UI状态
        self.is_training = True
        self.status_label.config(text="正在训练模型...")
        self.train_button.config(state='disabled')
        self.stop_button.config(state='normal')

        # 清空之前的结果
        self._clear_results_display()

        # 发布训练开始事件
        event_manager = get_event_manager()
        event_manager.publish(EventTypes.MODEL_TRAINING_STARTED, {
            'selected_models': selected_models,
            'config': self.training_config
        })

        # 开始训练
        self._run_training(selected_models)

    def _stop_training(self):
        """停止训练"""
        self.is_training = False
        self.status_label.config(text="训练已停止")
        self.train_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self._log_message("训练已被用户停止")

    def _run_training(self, selected_models):
        """运行训练过程"""
        def train():
            try:
                self._log_message(f"开始训练 {len(selected_models)} 个模型...")
                self._log_message(f"训练配置: {self.training_config}")

                # 数据预处理
                if not self.preprocessed_data:
                    self._log_message("正在预处理数据...")
                    X = self.current_data.iloc[:, :-1]  # 假设最后一列是目标变量
                    y = self.current_data.iloc[:, -1]

                    X_train, X_test, y_train, y_test, scaler = preprocess_data(
                        X, y,
                        test_size=self.training_config['test_size'],
                        random_state=self.training_config['random_state'],
                        scaling_method=self.training_config['scaling_method']
                    )

                    self.preprocessed_data = {
                        'X_train': X_train, 'X_test': X_test,
                        'y_train': y_train, 'y_test': y_test,
                        'scaler': scaler
                    }
                    self._log_message(f"数据预处理完成: 训练集 {X_train.shape[0]} 样本, 测试集 {X_test.shape[0]} 样本")

                # 训练模型
                results = {}
                total_models = len(selected_models)

                for i, model_name in enumerate(selected_models):
                    if not self.is_training:
                        break

                    self._log_message(f"正在训练 {model_name} ({i+1}/{total_models})...")

                    # 更新进度
                    progress = (i / total_models) * 100
                    if self.main_frame:
                        self.main_frame.after(0, lambda p=progress: self.progress_widget.set_progress(p))

                    # 训练模型
                    start_time = time.time()

                    if HAS_ALGORITHMS and model_name in MODEL_TRAINERS:
                        trainer = MODEL_TRAINERS[model_name]
                        result = trainer.train_and_evaluate(
                            self.preprocessed_data['X_train'],
                            self.preprocessed_data['y_train'],
                            self.preprocessed_data['X_test'],
                            self.preprocessed_data['y_test']
                        )

                        training_time = time.time() - start_time

                        results[model_name] = {
                            'model': result,
                            'metrics': result.get('metrics', {}),
                            'training_time': training_time
                        }

                        # 记录结果
                        metrics = result.get('metrics', {})
                        self._log_message(f"{model_name} 训练完成 - "
                                        f"准确率: {metrics.get('accuracy', 0):.3f}, "
                                        f"F1: {metrics.get('f1', 0):.3f}, "
                                        f"用时: {training_time:.2f}s")
                    else:
                        # 模拟训练（如果算法模块不可用）
                        time.sleep(1)  # 模拟训练时间
                        training_time = time.time() - start_time

                        results[model_name] = {
                            'metrics': {
                                'accuracy': np.random.uniform(0.7, 0.95),
                                'f1': np.random.uniform(0.65, 0.9),
                                'auc': np.random.uniform(0.75, 0.98)
                            },
                            'training_time': training_time
                        }

                        metrics = results[model_name]['metrics']
                        self._log_message(f"{model_name} 训练完成 - "
                                        f"准确率: {metrics['accuracy']:.3f}, "
                                        f"F1: {metrics['f1']:.3f}, "
                                        f"用时: {training_time:.2f}s")

                # 训练完成
                if self.is_training:
                    if self.main_frame:
                        self.main_frame.after(0, lambda: self._training_completed(results))

            except Exception as e:
                if self.main_frame:
                    self.main_frame.after(0, lambda: self._training_failed(str(e)))

        # 在后台线程中运行训练
        training_thread = threading.Thread(target=train)
        training_thread.daemon = True
        training_thread.start()

    def _log_message(self, message):
        """添加日志消息"""
        if hasattr(self, 'log_text'):
            timestamp = time.strftime("%H:%M:%S")
            log_entry = f"[{timestamp}] {message}\n"

            self.log_text.config(state='normal')
            self.log_text.insert('end', log_entry)
            self.log_text.see('end')
            self.log_text.config(state='disabled')

    def _clear_results_display(self):
        """清空结果显示"""
        # 清空结果表格
        if hasattr(self, 'results_tree'):
            for item in self.results_tree.get_children():
                self.results_tree.delete(item)

        # 重置进度条
        if hasattr(self, 'progress_widget'):
            self.progress_widget.set_progress(0)

    def _training_completed(self, results):
        """训练完成处理"""
        self.training_results = results
        self.is_training = False

        # 更新UI状态
        self.status_label.config(text=f"✅ 训练完成！成功训练 {len(results)} 个模型")
        self.train_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.progress_widget.set_progress(100)

        # 更新结果表格
        self._update_results_table(results)

        # 记录完成信息
        self._log_message(f"所有模型训练完成！共训练 {len(results)} 个模型")

        # 发布训练完成事件
        event_manager = get_event_manager()
        event_manager.publish(EventTypes.MODEL_TRAINED, {
            'results': self.training_results,
            'config': self.training_config
        })

    def _training_failed(self, error_message):
        """训练失败处理"""
        self.is_training = False
        self.status_label.config(text=f"❌ 训练失败: {error_message}")
        self.train_button.config(state='normal')
        self.stop_button.config(state='disabled')

        self._log_message(f"训练失败: {error_message}")
        self.show_error("训练失败", f"模型训练过程中出现错误:\n{error_message}")

    def _update_results_table(self, results):
        """更新结果表格"""
        if not hasattr(self, 'results_tree'):
            return

        # 清空现有结果
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # 添加新结果
        for model_name, result in results.items():
            metrics = result.get('metrics', {})
            training_time = result.get('training_time', 0)

            values = (
                model_name,
                f"{metrics.get('accuracy', 0):.3f}",
                f"{metrics.get('f1', 0):.3f}",
                f"{metrics.get('auc', 0):.3f}",
                f"{training_time:.2f}s"
            )

            self.results_tree.insert('', 'end', values=values)
    
    def _training_completed(self, results):
        """训练完成"""
        self.training_results = results
        
        # 更新状态
        self.status_label.config(text=f"训练完成！成功训练 {len(results)} 个模型")
        self.train_button.config(state='normal')
        
        # 显示结果
        self.results_text.config(state='normal')
        self.results_text.delete('1.0', 'end')
        
        result_text = "训练结果：\n\n"
        for model, metrics in results.items():
            result_text += f"{model}:\n"
            result_text += f"  准确率: {metrics['accuracy']:.3f}\n"
            result_text += f"  F1分数: {metrics['f1_score']:.3f}\n\n"
        
        self.results_text.insert('1.0', result_text)
        self.results_text.config(state='disabled')
        
        # 发布训练完成事件
        event_manager = get_event_manager()
        event_manager.publish(EventTypes.MODEL_TRAINED, {
            'results': self.training_results
        })
    
    def _training_failed(self, error_message):
        """训练失败"""
        self.status_label.config(text=f"训练失败: {error_message}")
        self.train_button.config(state='normal')
        self.show_error("训练失败", f"模型训练过程中出现错误:\n{error_message}")
    
    def _on_data_loaded(self, event_data):
        """数据加载事件处理"""
        try:
            if event_data and 'data' in event_data:
                self.current_data = event_data['data']
                self.preprocessed_data = None  # 重置预处理数据

                # 更新状态
                rows, cols = self.current_data.shape
                self.status_label.config(text=f"✅ 数据已加载: {rows} 行, {cols} 列")
                self.train_button.config(state='normal')

                # 记录日志
                self._log_message(f"数据已加载: {rows} 行, {cols} 列")
                self.logger.info(f"模型训练模块已加载数据: {self.current_data.shape}")

                # 默认选择几个常用模型
                if hasattr(self, 'model_vars'):
                    default_models = ['RandomForest', 'XGBoost', 'LogisticRegression']
                    for model_name in default_models:
                        if model_name in self.model_vars:
                            self.model_vars[model_name].set(True)
            else:
                self.logger.warning("数据加载事件中缺少'data'字段")
                self.status_label.config(text="❌ 数据加载失败")
                self._log_message("数据加载失败: 缺少数据字段")
        except Exception as e:
            self.logger.error(f"处理数据加载事件时出错: {e}")
            self.status_label.config(text="❌ 数据加载失败")
            self._log_message(f"数据加载失败: {e}")
    
    def _on_data_preprocessed(self, event_data):
        """数据预处理事件处理"""
        try:
            if event_data and all(key in event_data for key in ['X_train', 'X_test', 'y_train', 'y_test']):
                # 保存预处理后的数据
                self.preprocessed_data = event_data

                # 更新状态显示
                train_size = event_data['X_train'].shape[0]
                test_size = event_data['X_test'].shape[0]
                feature_count = event_data['X_train'].shape[1]

                self.status_label.config(
                    text=f"✅ 数据已预处理 (训练集: {train_size} 行, 测试集: {test_size} 行, 特征: {feature_count} 个)"
                )
                self.train_button.config(state='normal')

                # 记录日志
                self._log_message(f"数据已预处理: 训练集 {train_size} 行, 测试集 {test_size} 行, 特征 {feature_count} 个")
                self.logger.info(f"模型训练模块已接收预处理数据: 训练集{train_size}行, 测试集{test_size}行")

            elif event_data and 'data' in event_data:
                # 如果只有原始数据，显示预处理完成但未分割
                data = event_data['data']
                self.current_data = data
                self.status_label.config(text=f"✅ 数据已预处理 ({data.shape[0]} 行, {data.shape[1]} 列)")
                self.train_button.config(state='normal')
                self._log_message(f"数据已预处理: {data.shape[0]} 行, {data.shape[1]} 列")
                self.logger.info(f"模型训练模块已接收预处理数据: {data.shape}")
            else:
                self.logger.warning("数据预处理事件中缺少必要字段")
                self.status_label.config(text="❌ 数据预处理失败")
                self._log_message("数据预处理失败: 缺少必要字段")
        except Exception as e:
            self.logger.error(f"处理数据预处理事件时出错: {e}")
            self.status_label.config(text="❌ 数据预处理失败")
            self._log_message(f"数据预处理失败: {e}")
    
    def get_current_data(self) -> Optional[Dict[str, Any]]:
        """获取当前数据"""
        return {'dataframe': self.current_data} if self.current_data is not None else None

    def get_preprocessed_data(self) -> Optional[Dict[str, Any]]:
        """获取预处理后的数据"""
        return self.preprocessed_data.copy() if self.preprocessed_data else None

    def get_training_results(self) -> Dict[str, Any]:
        """获取训练结果"""
        return self.training_results.copy()

    def get_training_config(self) -> Dict[str, Any]:
        """获取训练配置"""
        return self.training_config.copy()

    def clear_results(self):
        """清空所有结果"""
        self.training_results = {}
        self.preprocessed_data = None

        # 清空结果显示
        self._clear_results_display()

        # 清空日志
        if hasattr(self, 'log_text'):
            self.log_text.config(state='normal')
            self.log_text.delete('1.0', 'end')
            self.log_text.insert('1.0', "训练日志将在这里显示...\n")
            self.log_text.config(state='disabled')

        # 重置状态
        if hasattr(self, 'status_label'):
            self.status_label.config(text="已清空结果")

        self._log_message("所有结果已清空")

    def _start_training(self):
        """开始训练"""
        if self.is_training:
            self.show_warning("警告", "训练正在进行中，请等待完成")
            return

        # 检查是否有选择的模型
        selected_models = self._get_selected_models()
        if not selected_models:
            self.show_warning("警告", "请至少选择一个模型进行训练")
            return

        # 检查数据是否准备好
        if not self._is_data_ready():
            self.show_warning("警告", "请先加载并预处理数据")
            return

        # 更新UI状态
        self.is_training = True
        self.train_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.progress_widget.reset()

        # 清空之前的结果
        self.training_results = {}
        self._clear_results_display()

        # 记录开始训练
        self._log_message(f"开始训练 {len(selected_models)} 个模型: {', '.join(selected_models)}")
        self.status_label.config(text="🚀 正在训练模型...")

        # 在后台线程中进行训练
        self.training_thread = threading.Thread(
            target=self._train_models_thread,
            args=(selected_models,),
            daemon=True
        )
        self.training_thread.start()

    def _stop_training(self):
        """停止训练"""
        if self.is_training:
            self.is_training = False
            self._log_message("用户停止了训练")
            self.status_label.config(text="⏹️ 训练已停止")

            # 恢复UI状态
            self.train_button.config(state='normal')
            self.stop_button.config(state='disabled')

    def _get_selected_models(self) -> List[str]:
        """获取选择的模型列表"""
        selected = []
        for model_name, var in self.model_vars.items():
            if var.get():
                selected.append(model_name)
        return selected

    def _is_data_ready(self) -> bool:
        """检查数据是否准备好"""
        if self.preprocessed_data:
            # 检查预处理数据是否完整
            required_keys = ['X_train', 'X_test', 'y_train', 'y_test']
            return all(key in self.preprocessed_data for key in required_keys)
        elif self.current_data is not None:
            # 如果有原始数据，可以进行训练（需要内部处理数据分割）
            return True
        return False

    def _train_models_thread(self, selected_models: List[str]):
        """训练模型的后台线程"""
        try:
            # 准备数据
            if not self._prepare_training_data():
                return

            total_models = len(selected_models)
            successful_models = 0

            for i, model_name in enumerate(selected_models):
                if not self.is_training:  # 检查是否被停止
                    break

                # 更新进度
                progress = (i / total_models) * 100
                if self.gui_updater:
                    self.gui_updater.safe_update_progress(self.progress_widget, progress)
                    self.gui_updater.safe_log_message(self._log_message, f"正在训练 {model_name}...")

                try:
                    # 训练模型
                    result = self._train_single_model(model_name)
                    if result:
                        self.training_results[model_name] = result
                        successful_models += 1

                        if self.gui_updater:
                            self.gui_updater.safe_log_message(
                                self._log_message,
                                f"✅ {model_name} 训练完成 - 准确率: {result.get('accuracy', 0):.4f}"
                            )
                    else:
                        if self.gui_updater:
                            self.gui_updater.safe_log_message(
                                self._log_message,
                                f"❌ {model_name} 训练失败"
                            )

                except Exception as e:
                    if self.gui_updater:
                        self.gui_updater.safe_log_message(
                            self._log_message,
                            f"❌ {model_name} 训练失败: {str(e)}"
                        )
                    self.logger.error(f"训练 {model_name} 时出错: {e}")

            # 训练完成
            if self.gui_updater:
                self.gui_updater.safe_update_progress(self.progress_widget, 100, "训练完成")
                self.gui_updater.safe_log_message(
                    self._log_message,
                    f"🎉 训练完成！成功训练 {successful_models}/{total_models} 个模型"
                )
                self.gui_updater.safe_update_status(
                    self.status_label,
                    f"✅ 训练完成 ({successful_models}/{total_models} 个模型)"
                )

            # 更新结果显示
            if self.training_results:
                self._update_results_display()

        except Exception as e:
            if self.gui_updater:
                self.gui_updater.safe_log_message(self._log_message, f"❌ 训练过程出错: {str(e)}")
                self.gui_updater.safe_show_message("错误", f"训练失败: {str(e)}", "error")
            self.logger.error(f"训练过程出错: {e}")
        finally:
            # 恢复UI状态
            self.is_training = False
            if self.gui_updater:
                self.gui_updater.safe_enable_widget(self.train_button, True)
                self.gui_updater.safe_enable_widget(self.stop_button, False)

    def _prepare_training_data(self) -> bool:
        """准备训练数据"""
        try:
            if self.preprocessed_data:
                # 使用预处理后的数据
                self.X_train = self.preprocessed_data['X_train']
                self.X_test = self.preprocessed_data['X_test']
                self.y_train = self.preprocessed_data['y_train']
                self.y_test = self.preprocessed_data['y_test']
                self.feature_names = self.preprocessed_data.get('feature_names', list(self.X_train.columns))
                self.target_column = self.preprocessed_data.get('target_column', 'target')

                if self.gui_updater:
                    self.gui_updater.safe_log_message(
                        self._log_message,
                        f"使用预处理数据: 训练集 {self.X_train.shape[0]} 行, 测试集 {self.X_test.shape[0]} 行"
                    )
                return True

            elif self.current_data is not None:
                # 使用原始数据，需要进行数据分割
                if self.gui_updater:
                    self.gui_updater.safe_log_message(self._log_message, "正在分割原始数据...")

                # 简单的数据分割（假设最后一列是目标变量）
                X = self.current_data.iloc[:, :-1]
                y = self.current_data.iloc[:, -1]

                from sklearn.model_selection import train_test_split
                self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
                    X, y, test_size=self.training_config['test_size'],
                    random_state=self.training_config['random_state']
                )

                self.feature_names = list(X.columns)
                self.target_column = y.name if hasattr(y, 'name') else 'target'

                if self.gui_updater:
                    self.gui_updater.safe_log_message(
                        self._log_message,
                        f"数据分割完成: 训练集 {self.X_train.shape[0]} 行, 测试集 {self.X_test.shape[0]} 行"
                    )
                return True
            else:
                if self.gui_updater:
                    self.gui_updater.safe_log_message(self._log_message, "❌ 没有可用的训练数据")
                return False

        except Exception as e:
            if self.gui_updater:
                self.gui_updater.safe_log_message(self._log_message, f"❌ 数据准备失败: {str(e)}")
            self.logger.error(f"数据准备失败: {e}")
            return False

    def _train_single_model(self, model_name: str) -> Optional[Dict[str, Any]]:
        """训练单个模型"""
        try:
            if not HAS_ALGORITHMS or model_name not in MODEL_TRAINERS:
                self.logger.warning(f"模型 {model_name} 不可用")
                return None

            # 获取模型训练器
            trainer = MODEL_TRAINERS[model_name]

            # 训练模型
            start_time = time.time()
            model_result = trainer.train_and_evaluate(
                self.X_train, self.y_train, self.X_test, self.y_test
            )
            training_time = time.time() - start_time

            # 整理结果
            result = {
                'model': model_result,
                'trainer': trainer,
                'model_name': model_name,
                'training_time': training_time,
                'X_train': self.X_train,
                'X_test': self.X_test,
                'y_train': self.y_train,
                'y_test': self.y_test,
                'feature_names': self.feature_names,
                'target_column': self.target_column
            }

            # 提取性能指标
            if hasattr(model_result, 'metrics'):
                result.update(model_result.metrics)
            elif isinstance(model_result, dict):
                result.update(model_result)

            return result

        except Exception as e:
            self.logger.error(f"训练模型 {model_name} 时出错: {e}")
            return None

    def _select_all_models(self):
        """选择所有模型"""
        for var in self.model_vars.values():
            var.set(True)
        self._log_message("已选择所有模型")

    def _deselect_all_models(self):
        """取消选择所有模型"""
        for var in self.model_vars.values():
            var.set(False)
        self._log_message("已取消选择所有模型")

    def _log_message(self, message: str):
        """记录日志消息"""
        if hasattr(self, 'log_text'):
            try:
                self.log_text.config(state='normal')
                timestamp = time.strftime("%H:%M:%S")
                self.log_text.insert('end', f"[{timestamp}] {message}\n")
                self.log_text.see('end')
                self.log_text.config(state='disabled')
            except Exception as e:
                self.logger.error(f"记录日志失败: {e}")

    def _clear_results_display(self):
        """清空结果显示"""
        # 这里可以添加清空结果表格等UI元素的代码
        pass

    def _update_results_display(self):
        """更新结果显示"""
        # 这里可以添加更新结果表格等UI元素的代码
        pass

    def _bind_events(self):
        """绑定事件"""
        # 订阅数据相关事件
        self.subscribe_event(EventTypes.DATA_LOADED, self._on_data_loaded)
        self.subscribe_event(EventTypes.DATA_PREPROCESSED, self._on_data_preprocessed)

        # 订阅训练相关事件
        self.subscribe_event(EventTypes.MODEL_TRAINED, self._on_model_trained)
        self.subscribe_event(EventTypes.TRAINING_COMPLETED, self._on_training_completed)

    def _on_model_trained(self, event_data):
        """单个模型训练完成事件处理"""
        if event_data and 'model_name' in event_data:
            model_name = event_data['model_name']
            self._log_message(f"模型 {model_name} 训练完成")

    def _on_training_completed(self, event_data):
        """所有训练完成事件处理"""
        self._log_message("所有模型训练完成")
        if event_data and 'results' in event_data:
            self.training_results.update(event_data['results'])
            self._update_results_display()

    def is_training_active(self) -> bool:
        """检查是否正在训练"""
        return self.is_training
